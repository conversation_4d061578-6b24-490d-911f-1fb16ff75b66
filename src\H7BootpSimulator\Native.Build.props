<Project>
  <PropertyGroup Condition="'$(NativeBuild)'=='true'">
    <PublishAot>true</PublishAot>
    <IlcOptimizationPreference>Size</IlcOptimizationPreference>
    <IlcFoldIdenticalMethodBodies>true</IlcFoldIdenticalMethodBodies>
    <StaticallyLinked Condition="$(RuntimeIdentifier.StartsWith('win'))">true</StaticallyLinked>
    <StripSymbols>true</StripSymbols>
  </PropertyGroup>

  <ItemGroup Condition="'$(NativeBuild)'=='true'">
    <!-- <RdXmlFile Include="rd.xml" /> -->
    <IlcArg Include="--stacktracedata" />
  </ItemGroup>

</Project>