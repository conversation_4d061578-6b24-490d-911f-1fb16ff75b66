///////////////////////////////////////////////////////////////////////////////
// TOOLS / ADDINS
///////////////////////////////////////////////////////////////////////////////

#tool dotnet:?package=GitVersion.Tool&version=6.3.0
#addin nuget:?package=Cake.7zip&version=5.0.0

///////////////////////////////////////////////////////////////////////////////
// ARGUMENTS
///////////////////////////////////////////////////////////////////////////////

using Spectre.Console;

var targets = Arguments<string>("target", "Default");
var verbosity = DotNetVerbosity.Minimal;
var description = Argument("description", "H7固件升级模拟器");

///////////////////////////////////////////////////////////////////////////////
// PREPARATION
///////////////////////////////////////////////////////////////////////////////

var repoName = "H7BootpSimulator";
var isLocal = BuildSystem.IsLocalBuild;

// // Set build version
GitVersion gitVersion = GitVersion(new GitVersionSettings { OutputType = GitVersionOutput.Json });

var branchName = gitVersion.BranchName;
var isDevelopBranch = StringComparer.OrdinalIgnoreCase.Equals("develop", branchName);
var isReleaseBranch = StringComparer.OrdinalIgnoreCase.Equals("master", branchName);

// Directories and Paths
var solution = "./H7BootpSimulator.csproj";
var solution_dev = "./H7BootpSimulator.csproj";

///////////////////////////////////////////////////////////////////////////////
// SETUP / TEARDOWN
///////////////////////////////////////////////////////////////////////////////

Setup(ctx =>
{
    // Figlet Logo
    AnsiConsole.Write(new FigletText($"{repoName} (*^-^*)").LeftJustified().Color(Color.Green));

    Information("Informational   Version: {0}", gitVersion.InformationalVersion);
    Information("SemVer          Version: {0}", gitVersion.SemVer);
    Information("FullSemVer      Version: {0}", gitVersion.FullSemVer);
    Information("AssemblySemVer  Version: {0}", gitVersion.AssemblySemVer);
    Information("MajorMinorPatch Version: {0}", gitVersion.MajorMinorPatch);
    Information("Targets                : {0}", string.Join(",", targets));
    Information("Branch                 : {0}", branchName);
    Information("IsDevelopBranch        : {0}", isDevelopBranch);
    Information("OsReleaseBranch        : {0}", isReleaseBranch);
});

Teardown(ctx =>
{
});

///////////////////////////////////////////////////////////////////////////////
// TASKS
///////////////////////////////////////////////////////////////////////////////

Task("Ver")
    .Description("show gitversion")
    .ContinueOnError()
    .Does(() =>
    {
    });

Task("Clean")
    .Description($"clean installer.")
    .Does(() =>
    {
        var directoriesToDelete = GetDirectories("./publish")
            .Concat(GetDirectories("./**/bin"))
            .Concat(GetDirectories("./**/obj"))
            .Concat(GetDirectories("./installer/NSIS"))
            .Concat(GetDirectories("./installer/temp"));
        foreach (var item in directoriesToDelete)
        {
            Information(item);
        }
        DeleteDirectories(directoriesToDelete, new DeleteDirectorySettings { Recursive = true, Force = true });
    });

Task("Debug")
    .Description($"build with debug configuration. [{solution_dev}]")
    .Does(() =>
    {
        var buildConfiguration = Argument("configuration", "Debug");

        foreach (var r in new string[] { "win-x64" })
        {
            var msBuildSettings = new DotNetMSBuildSettings
            {
                ArgumentCustomization = args => args
                    .Append("-nodeReuse:false")
                ,
                BinaryLogger = new MSBuildBinaryLoggerSettings() { Enabled = isLocal }
            };

            msBuildSettings = msBuildSettings
                .SetMaxCpuCount(2)
                .WithProperty("SelfContained", "false")
                .WithProperty("RuntimeIdentifier", r)
                .WithProperty("Description", description)
                .WithProperty("Version", gitVersion.MajorMinorPatch)
                .WithProperty("AssemblyVersion", gitVersion.AssemblySemVer)
                .WithProperty("FileVersion", gitVersion.AssemblySemFileVer)
                .WithProperty("InformationalVersion", gitVersion.InformationalVersion);

            var buildSetting = new DotNetBuildSettings
            {
                MSBuildSettings = msBuildSettings,
                Configuration = buildConfiguration,
                OutputDirectory = $@"bin\{r}\{buildConfiguration}\",
                Verbosity = verbosity,
            };
            DotNetBuild(solution_dev, buildSetting);
        }
    });

Task("Publish_win_x64")
    .Description($"publish release configuration. [{solution}] .")
    .Does(() =>
    {
        var buildConfiguration = Argument("configuration", "Release");
        var msBuildSettings = new DotNetMSBuildSettings
        {
            ArgumentCustomization = args => args
                .Append("-nodeReuse:false")
            ,
            BinaryLogger = new MSBuildBinaryLoggerSettings() { Enabled = isLocal }
        };

        msBuildSettings = msBuildSettings
            .SetMaxCpuCount(2)
            .WithProperty("DebugType", "none")
            .WithProperty("DebugSymbols", "false")
            .WithProperty("Description", description)
            .WithProperty("Version", gitVersion.MajorMinorPatch)
            .WithProperty("AssemblyVersion", gitVersion.AssemblySemVer)
            .WithProperty("FileVersion", gitVersion.AssemblySemFileVer)
            .WithProperty("InformationalVersion", gitVersion.InformationalVersion)
            .WithProperty("NativeBuild", "false")
            .WithProperty("TieredPGO", "true");

        foreach (var r in new string[] { "win-x64" })
        {
            var setting = new DotNetPublishSettings
            {
                MSBuildSettings = msBuildSettings,
                Configuration = buildConfiguration,
                Framework = "net9.0",
                Runtime = r,
                OutputDirectory = $@"Publish\{r}\{buildConfiguration}\",
                PublishSingleFile = true,
                SelfContained = true,
                PublishReadyToRun = true,
                IncludeNativeLibrariesForSelfExtract = true,
                EnableCompressionInSingleFile = false,
                PublishTrimmed = true,
                Verbosity = verbosity,
            };
            DotNetPublish(solution, setting);
        }
    });

Task("Publish_win_x64_Aot")
    .Description($"publish release configuration. [{solution}] .")
    .Does(() =>
    {
        var buildConfiguration = Argument("configuration", "Release");
        var msBuildSettings = new DotNetMSBuildSettings
        {
            ArgumentCustomization = args => args
                .Append("-nodeReuse:false")
            ,
            BinaryLogger = new MSBuildBinaryLoggerSettings() { Enabled = isLocal }
        };

        msBuildSettings = msBuildSettings
            .SetMaxCpuCount(2)
            .WithProperty("DebugType", "none")
            .WithProperty("DebugSymbols", "false")
            .WithProperty("Description", description)
            .WithProperty("Version", gitVersion.MajorMinorPatch)
            .WithProperty("AssemblyVersion", gitVersion.AssemblySemVer)
            .WithProperty("FileVersion", gitVersion.AssemblySemFileVer)
            .WithProperty("InformationalVersion", gitVersion.InformationalVersion)
            .WithProperty("NativeBuild", "true");

        foreach (var r in new string[] { "win-x64" })
        {
            var setting = new DotNetPublishSettings
            {
                MSBuildSettings = msBuildSettings,
                Configuration = buildConfiguration,
                Framework = "net9.0",
                Runtime = r,
                PublishTrimmed = true,
                SelfContained = true,
                PublishReadyToRun = true,
                OutputDirectory = $@"Publish\{r}-aot\{buildConfiguration}\",
                Verbosity = verbosity,
            };
            DotNetPublish(solution, setting);
        }
    });

Task("PublishWindowsX64Pack")
    .IsDependentOn("Publish_win_x64")
    .Does(()=>
    {
        var archivePath = $"Publish/h7bootpsim-{gitVersion.InformationalVersion}-win-x64.7z";
        try{ DeleteFile(archivePath); } catch {}
        SevenZip(m => m
            .InAddMode()
            .WithArchive(File(archivePath))
            .WithFiles(File(System.IO.Path.GetFullPath("Publish/win-x64/Release/h7bootpsim.exe")))
            .WithArchiveType(SwitchArchiveType.SevenZip)
            .WithCompressionMethodLevel(9));
    });

Task("PublishWindowsX64AotPack")
    .IsDependentOn("Publish_win_x64_Aot")
    .Does(()=>
    {
        var archivePath = $"Publish/h7bootpsim-{gitVersion.InformationalVersion}-win-x64-aot.7z";
        try{ DeleteFile(archivePath); } catch {}
        SevenZip(m => m
            .InAddMode()
            .WithArchive(File(archivePath))
            .WithFiles(
                File(System.IO.Path.GetFullPath("Publish/win-x64-aot/Release/h7bootpsim.exe"))
            )
            .WithArchiveType(SwitchArchiveType.SevenZip)
            .WithCompressionMethodLevel(9));
    });

///////////////////////////////////////////////////////////////////////////////
// TASK TARGETS
///////////////////////////////////////////////////////////////////////////////

Task("PublishAotWindows")
    .Description("Publish and pack.")
    .IsDependentOn("PublishWindowsX64AotPack");

Task("PublishCross")
    .IsDependentOn("PublishWindowsX64Pack");

Task("PublishAotRunningInWindows")
    .IsDependentOn("PublishAotWindows");

Task("Publish")
    .IsDependentOn("Clean")
    .IsDependentOn("PublishCross")
    .IsDependentOn("PublishAotRunningInWindows");

Task("Default")
    .IsDependentOn("Clean")
    .IsDependentOn("Publish");

///////////////////////////////////////////////////////////////////////////////
// EXECUTION
///////////////////////////////////////////////////////////////////////////////

RunTargets(targets);
