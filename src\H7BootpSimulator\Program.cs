using System.Buffers;
using System.CommandLine;
using System.Net.Sockets;
using System.Net;
using CoreLib.Utils;
using Tftp.Net;

ushort BOOTP_LOCAL_PORT = 9;
ushort TFTP_LOCAL_PORT = 69;

var localOption = new Option<IPAddress>("--local")
{
    DefaultValueFactory = _ => IPAddress.Any,
    CustomParser = p => IPAddress.Parse(p.Tokens.First().Value)
};
var bootpLocalPortOption = new Option<ushort>("--bootp-local-port")
{
    Required = false,
    DefaultValueFactory = _ => BOOTP_LOCAL_PORT
};
var tftpRemotePortOption = new Option<ushort>("--tftp-remote-port")
{
    Required = false,
    DefaultValueFactory = _ => TFTP_LOCAL_PORT
};
var forUnitTestOption = new Option<bool>("--for-unit-test")
{
    Required = false,
    Arity = ArgumentArity.Zero,
    DefaultValueFactory = _ => false
};
var saveFilePathOption = new Option<string>("--save-file-path")
{
    Required = false,
};

var rootCommand = new RootCommand("Bootp Simulator");
rootCommand.Options.Add(localOption);
rootCommand.Options.Add(bootpLocalPortOption);
rootCommand.Options.Add(tftpRemotePortOption);
rootCommand.Options.Add(forUnitTestOption);
rootCommand.Options.Add(saveFilePathOption);

// handler
rootCommand.SetAction(async (parseResult, token) =>
{
    var local = parseResult.GetRequiredValue(localOption);
    var bootpLocalPort = parseResult.GetRequiredValue(bootpLocalPortOption);
    var tftpRemotePort = parseResult.GetRequiredValue(tftpRemotePortOption);
    var forUnitTest = parseResult.GetRequiredValue(forUnitTestOption);
    var saveFilePath = parseResult.GetValue(saveFilePathOption);

    // 接收数据
    using var socket = new Socket(AddressFamily.InterNetwork, SocketType.Dgram, ProtocolType.Udp);
    socket.EnableBroadcast = true;
    socket.Bind(new IPEndPoint(local, bootpLocalPort));
    Console.WriteLine("Bootp bind to {0}", socket.LocalEndPoint);

    // receive
    do
    {
        using var buffer = MemoryPool<byte>.Shared.Rent(1500);

        IPEndPoint remoteEp;

        #region Bootp

        {
            // receive start
            {
                var (success, r) = await socket.ReceiveFromAsync(buffer.Memory, SocketFlags.None, new IPEndPoint(IPAddress.Any, 0), CancellationToken.None).AsTask().ContinueWith(t => (t.IsCompletedSuccessfully, t.IsCompletedSuccessfully ? t.Result : default));
                if (!success)
                {
                    continue;
                }
                remoteEp = (IPEndPoint)r.RemoteEndPoint;
                Console.WriteLine("Rx<{0}>[{1}]", r.RemoteEndPoint, r.ReceivedBytes);
                Console.WriteLine(buffer.Memory[..r.ReceivedBytes].ToHexString());
                Span<byte> start = [0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA];
                if (!(r.ReceivedBytes == 30 && buffer.Memory.Span.StartsWith(start)))
                {
                    continue;
                }
            }

            var mac = buffer.Memory[6..12];
        }

        #endregion

        #region Tftp

        {
            var tftp = new TftpClient(remoteEp.Address, tftpRemotePort);
            using var transfer = tftp.Download("firmware.bin");
            var waiter = new TaskCompletionSource<int>();
            transfer.OnProgress += OnProgress;
            transfer.OnFinished += OnFinished;
            transfer.OnError += OnError;
            var filePath = saveFilePath ?? Path.GetTempFileName();
            try
            {
                using var fs = File.OpenWrite(filePath);
                using var cts = new CancellationTokenSource(60_000);
                using var registration = cts.Token.Register(() => waiter.TrySetCanceled());
                transfer.Start(fs);
                await waiter.Task;
                await Task.Delay(1000);
            }
            finally
            {
                if (saveFilePath is null)
                {
                    File.Delete(filePath);
                }
            }

            static void OnProgress(ITftpTransfer transfer, TftpTransferProgress progress) => Console.WriteLine("Progress: {0:0.00%}", (double)progress.TransferredBytes / progress.TotalBytes);
            void OnFinished(ITftpTransfer transfer)
            {
                Console.WriteLine("Progress: {0:0.00%}", 1);
                waiter.TrySetResult(0);
            }
            void OnError(ITftpTransfer transfer, TftpTransferError error) => waiter.TrySetException(new Exception(error.ToString()));
        }

        #endregion

    }
    while (!forUnitTest);
});

return await new CommandLineConfiguration(rootCommand).InvokeAsync(args);
