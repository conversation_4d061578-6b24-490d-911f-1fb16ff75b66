// Copyright (c) USR. All rights reserved.

using System.Net;
using System.Text;

internal static class ConverterExtension
{
    public static uint IpAddressToUint(this IPAddress ip)
    {
        var bytes = ip.GetAddressBytes();
        return ((uint)bytes[3] << 24) | ((uint)bytes[2] << 16) | ((uint)bytes[1] << 8) | bytes[0];
    }

    public static unsafe string ReadCString(byte* ptr, Encoding encoding, int maxLength = int.MaxValue)
    {
        var p = ptr;
        var len = 0;
        do
        {
            if (*p == 0 || len >= maxLength)
            {
                break;
            }
            p++;
            len++;
        } while (true);
        return encoding.GetString(ptr, len);
    }

    public static unsafe void WriteCString(byte* ptr, string value, Encoding encoding, int maxLength = int.MaxValue)
    {
        fixed (char* pValue = value)
        {
            if (value.Length >= maxLength)
            {
                value = value.Substring(0, maxLength - 1);
            }
            var len = Encoding.ASCII.GetBytes(pValue, value.Length, ptr, maxLength - 1);
            *(ptr + len) = 0;
        }
    }
}
