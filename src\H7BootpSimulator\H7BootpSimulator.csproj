﻿<Project Sdk="Microsoft.NET.Sdk">
    <PropertyGroup>
        <OutputType>Exe</OutputType>
        <AssemblyName>h7bootpsim</AssemblyName>
        <EmitCompilerGeneratedFiles>true</EmitCompilerGeneratedFiles>
        <TrimMode>Link</TrimMode>
        <InvariantGlobalization>true</InvariantGlobalization>
        <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    </PropertyGroup>
    <ItemGroup>
        <PackageReference Include="System.CommandLine" Version="2.0.0-beta5.25306.1" />
        <PackageReference Include="Tftp.Net" Version="1.3.0" />
        <PackageReference Include="ZCStudio.CoreLib" Version="0.2.7" />
    </ItemGroup>
    <Import Project="Native.Build.props" />
</Project>